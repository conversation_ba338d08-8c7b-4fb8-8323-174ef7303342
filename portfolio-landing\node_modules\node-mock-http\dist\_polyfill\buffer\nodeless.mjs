const d=[],y=[],ot=typeof Uint8Array>"u"?Array:Uint8Array,L="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(let t=0,e=L.length;t<e;++t)d[t]=L[t],y[L.charCodeAt(t)]=t;y[45]=62,y[95]=63;function rt(t){const e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");let n=t.indexOf("=");n===-1&&(n=e);const o=n===e?0:4-n%4;return[n,o]}function it(t,e,n){return(e+n)*3/4-n}function ft(t){let e;const n=rt(t),o=n[0],r=n[1],i=new ot(it(t,o,r));let u=0;const h=r>0?o-4:o;let c;for(c=0;c<h;c+=4)e=y[t.charCodeAt(c)]<<18|y[t.charCodeAt(c+1)]<<12|y[t.charCodeAt(c+2)]<<6|y[t.charCodeAt(c+3)],i[u++]=e>>16&255,i[u++]=e>>8&255,i[u++]=e&255;return r===2&&(e=y[t.charCodeAt(c)]<<2|y[t.charCodeAt(c+1)]>>4,i[u++]=e&255),r===1&&(e=y[t.charCodeAt(c)]<<10|y[t.charCodeAt(c+1)]<<4|y[t.charCodeAt(c+2)]>>2,i[u++]=e>>8&255,i[u++]=e&255),i}function ut(t){return d[t>>18&63]+d[t>>12&63]+d[t>>6&63]+d[t&63]}function st(t,e,n){let o;const r=[];for(let i=e;i<n;i+=3)o=(t[i]<<16&16711680)+(t[i+1]<<8&65280)+(t[i+2]&255),r.push(ut(o));return r.join("")}function P(t){let e;const n=t.length,o=n%3,r=[],i=16383;for(let u=0,h=n-o;u<h;u+=i)r.push(st(t,u,u+i>h?h:u+i));return o===1?(e=t[n-1],r.push(d[e>>2]+d[e<<4&63]+"==")):o===2&&(e=(t[n-2]<<8)+t[n-1],r.push(d[e>>10]+d[e>>4&63]+d[e<<2&63]+"=")),r.join("")}/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */function R(t,e,n,o,r){let i,u;const h=r*8-o-1,c=(1<<h)-1,l=c>>1;let s=-7,a=n?r-1:0;const I=n?-1:1;let w=t[e+a];for(a+=I,i=w&(1<<-s)-1,w>>=-s,s+=h;s>0;)i=i*256+t[e+a],a+=I,s-=8;for(u=i&(1<<-s)-1,i>>=-s,s+=o;s>0;)u=u*256+t[e+a],a+=I,s-=8;if(i===0)i=1-l;else{if(i===c)return u?Number.NaN:(w?-1:1)*Number.POSITIVE_INFINITY;u=u+Math.pow(2,o),i=i-l}return(w?-1:1)*u*Math.pow(2,i-o)}function F(t,e,n,o,r,i){let u,h,c,l=i*8-r-1;const s=(1<<l)-1,a=s>>1,I=r===23?Math.pow(2,-24)-Math.pow(2,-77):0;let w=o?0:i-1;const O=o?1:-1,nt=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),Number.isNaN(e)||e===Number.POSITIVE_INFINITY?(h=Number.isNaN(e)?1:0,u=s):(u=Math.floor(Math.log2(e)),e*(c=Math.pow(2,-u))<1&&(u--,c*=2),e+=u+a>=1?I/c:I*Math.pow(2,1-a),e*c>=2&&(u++,c/=2),u+a>=s?(h=0,u=s):u+a>=1?(h=(e*c-1)*Math.pow(2,r),u=u+a):(h=e*Math.pow(2,a-1)*Math.pow(2,r),u=0));r>=8;)t[n+w]=h&255,w+=O,h/=256,r-=8;for(u=u<<r|h,l+=r;l>0;)t[n+w]=u&255,w+=O,u/=256,l-=8;t[n+w-O]|=nt*128}const k=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null,ht=50,N=2147483647;f.TYPED_ARRAY_SUPPORT=ct(),!f.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This environment lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function ct(){try{const t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),t.foo()===42}catch{return!1}}Object.defineProperty(f.prototype,"parent",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.buffer}}),Object.defineProperty(f.prototype,"offset",{enumerable:!0,get:function(){if(f.isBuffer(this))return this.byteOffset}});function m(t){if(t>N)throw new RangeError('The value "'+t+'" is invalid for option "size"');const e=new Uint8Array(t);return Object.setPrototypeOf(e,f.prototype),e}function f(t,e,n){if(typeof t=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return S(t)}return j(t,e,n)}f.poolSize=8192;function j(t,e,n){if(typeof t=="string")return lt(t,e);if(ArrayBuffer.isView(t))return pt(t);if(t==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(b(t,ArrayBuffer)||t&&b(t.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(b(t,SharedArrayBuffer)||t&&b(t.buffer,SharedArrayBuffer)))return Y(t,e,n);if(typeof t=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');const o=t.valueOf&&t.valueOf();if(o!=null&&o!==t)return f.from(o,e,n);const r=gt(t);if(r)return r;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof t[Symbol.toPrimitive]=="function")return f.from(t[Symbol.toPrimitive]("string"),e,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}f.from=function(t,e,n){return j(t,e,n)},Object.setPrototypeOf(f.prototype,Uint8Array.prototype),Object.setPrototypeOf(f,Uint8Array);function D(t){if(typeof t!="number")throw new TypeError('"size" argument must be of type number');if(t<0)throw new RangeError('The value "'+t+'" is invalid for option "size"')}function at(t,e,n){return D(t),t<=0?m(t):e!==void 0?typeof n=="string"?m(t).fill(e,n):m(t).fill(e):m(t)}f.alloc=function(t,e,n){return at(t,e,n)};function S(t){return D(t),m(t<0?0:x(t)|0)}f.allocUnsafe=function(t){return S(t)},f.allocUnsafeSlow=function(t){return S(t)};function lt(t,e){if((typeof e!="string"||e==="")&&(e="utf8"),!f.isEncoding(e))throw new TypeError("Unknown encoding: "+e);const n=z(t,e)|0;let o=m(n);const r=o.write(t,e);return r!==n&&(o=o.slice(0,r)),o}function _(t){const e=t.length<0?0:x(t.length)|0,n=m(e);for(let o=0;o<e;o+=1)n[o]=t[o]&255;return n}function pt(t){if(b(t,Uint8Array)){const e=new Uint8Array(t);return Y(e.buffer,e.byteOffset,e.byteLength)}return _(t)}function Y(t,e,n){if(e<0||t.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(n||0))throw new RangeError('"length" is outside of buffer bounds');let o;return e===void 0&&n===void 0?o=new Uint8Array(t):n===void 0?o=new Uint8Array(t,e):o=new Uint8Array(t,e,n),Object.setPrototypeOf(o,f.prototype),o}function gt(t){if(f.isBuffer(t)){const e=x(t.length)|0,n=m(e);return n.length===0||t.copy(n,0,0,e),n}if(t.length!==void 0)return typeof t.length!="number"||M(t.length)?m(0):_(t);if(t.type==="Buffer"&&Array.isArray(t.data))return _(t.data)}function x(t){if(t>=N)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+N.toString(16)+" bytes");return t|0}f.isBuffer=function(t){return t!=null&&t._isBuffer===!0&&t!==f.prototype},f.compare=function(t,e){if(b(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),b(e,Uint8Array)&&(e=f.from(e,e.offset,e.byteLength)),!f.isBuffer(t)||!f.isBuffer(e))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;let n=t.length,o=e.length;for(let r=0,i=Math.min(n,o);r<i;++r)if(t[r]!==e[r]){n=t[r],o=e[r];break}return n<o?-1:o<n?1:0},f.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(t,e){if(!Array.isArray(t))throw new TypeError('"list" argument must be an Array of Buffers');if(t.length===0)return f.alloc(0);let n;if(e===void 0)for(e=0,n=0;n<t.length;++n)e+=t[n].length;const o=f.allocUnsafe(e);let r=0;for(n=0;n<t.length;++n){let i=t[n];if(b(i,Uint8Array))r+i.length>o.length?(f.isBuffer(i)||(i=f.from(i.buffer,i.byteOffset,i.byteLength)),i.copy(o,r)):Uint8Array.prototype.set.call(o,i,r);else if(f.isBuffer(i))i.copy(o,r);else throw new TypeError('"list" argument must be an Array of Buffers');r+=i.length}return o};function z(t,e){if(f.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||b(t,ArrayBuffer))return t.byteLength;if(typeof t!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);const n=t.length,o=arguments.length>2&&arguments[2]===!0;if(!o&&n===0)return 0;let r=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return n*2;case"hex":return n>>>1;case"base64":return et(t).length;default:{if(r)return o?-1:$(t).length;e=(""+e).toLowerCase(),r=!0}}}f.byteLength=z;function yt(t,e,n){let o=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((n===void 0||n>this.length)&&(n=this.length),n<=0)||(n>>>=0,e>>>=0,n<=e))return"";for(t||(t="utf8");;)switch(t){case"hex":return vt(this,e,n);case"utf8":case"utf-8":return q(this,e,n);case"ascii":return At(this,e,n);case"latin1":case"binary":return Ut(this,e,n);case"base64":return Bt(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Rt(this,e,n);default:{if(o)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),o=!0}}}f.prototype._isBuffer=!0;function B(t,e,n){const o=t[e];t[e]=t[n],t[n]=o}f.prototype.swap16=function(){const t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let e=0;e<t;e+=2)B(this,e,e+1);return this},f.prototype.swap32=function(){const t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let e=0;e<t;e+=4)B(this,e,e+3),B(this,e+1,e+2);return this},f.prototype.swap64=function(){const t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let e=0;e<t;e+=8)B(this,e,e+7),B(this,e+1,e+6),B(this,e+2,e+5),B(this,e+3,e+4);return this},f.prototype.toString=function(){const t=this.length;return t===0?"":arguments.length===0?q(this,0,t):Reflect.apply(yt,this,arguments)},f.prototype.toLocaleString=f.prototype.toString,f.prototype.equals=function(t){if(!f.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t?!0:f.compare(this,t)===0},f.prototype.inspect=function(){let t="";const e=ht;return t=this.toString("hex",0,e).replace(/(.{2})/g,"$1 ").trim(),this.length>e&&(t+=" ... "),"<Buffer "+t+">"},k&&(f.prototype[k]=f.prototype.inspect),f.prototype.compare=function(t,e,n,o,r){if(b(t,Uint8Array)&&(t=f.from(t,t.offset,t.byteLength)),!f.isBuffer(t))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(e===void 0&&(e=0),n===void 0&&(n=t?t.length:0),o===void 0&&(o=0),r===void 0&&(r=this.length),e<0||n>t.length||o<0||r>this.length)throw new RangeError("out of range index");if(o>=r&&e>=n)return 0;if(o>=r)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,o>>>=0,r>>>=0,this===t)return 0;let i=r-o,u=n-e;const h=Math.min(i,u),c=this.slice(o,r),l=t.slice(e,n);for(let s=0;s<h;++s)if(c[s]!==l[s]){i=c[s],u=l[s];break}return i<u?-1:u<i?1:0};function V(t,e,n,o,r){if(t.length===0)return-1;if(typeof n=="string"?(o=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,M(n)&&(n=r?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(r)return-1;n=t.length-1}else if(n<0)if(r)n=0;else return-1;if(typeof e=="string"&&(e=f.from(e,o)),f.isBuffer(e))return e.length===0?-1:G(t,e,n,o,r);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?r?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):G(t,[e],n,o,r);throw new TypeError("val must be string, number or Buffer")}function G(t,e,n,o,r){let i=1,u=t.length,h=e.length;if(o!==void 0&&(o=String(o).toLowerCase(),o==="ucs2"||o==="ucs-2"||o==="utf16le"||o==="utf-16le")){if(t.length<2||e.length<2)return-1;i=2,u/=2,h/=2,n/=2}function c(s,a){return i===1?s[a]:s.readUInt16BE(a*i)}let l;if(r){let s=-1;for(l=n;l<u;l++)if(c(t,l)===c(e,s===-1?0:l-s)){if(s===-1&&(s=l),l-s+1===h)return s*i}else s!==-1&&(l-=l-s),s=-1}else for(n+h>u&&(n=u-h),l=n;l>=0;l--){let s=!0;for(let a=0;a<h;a++)if(c(t,l+a)!==c(e,a)){s=!1;break}if(s)return l}return-1}f.prototype.includes=function(t,e,n){return this.indexOf(t,e,n)!==-1},f.prototype.indexOf=function(t,e,n){return V(this,t,e,n,!0)},f.prototype.lastIndexOf=function(t,e,n){return V(this,t,e,n,!1)};function wt(t,e,n,o){n=Number(n)||0;const r=t.length-n;o?(o=Number(o),o>r&&(o=r)):o=r;const i=e.length;o>i/2&&(o=i/2);let u;for(u=0;u<o;++u){const h=Number.parseInt(e.slice(u*2,u*2+2),16);if(M(h))return u;t[n+u]=h}return u}function dt(t,e,n,o){return T($(e,t.length-n),t,n,o)}function bt(t,e,n,o){return T(Nt(e),t,n,o)}function mt(t,e,n,o){return T(et(e),t,n,o)}function Et(t,e,n,o){return T(St(e,t.length-n),t,n,o)}f.prototype.write=function(t,e,n,o){if(e===void 0)o="utf8",n=this.length,e=0;else if(n===void 0&&typeof e=="string")o=e,n=this.length,e=0;else if(Number.isFinite(e))e=e>>>0,Number.isFinite(n)?(n=n>>>0,o===void 0&&(o="utf8")):(o=n,n=void 0);else throw new TypeError("Buffer.write(string, encoding, offset[, length]) is no longer supported");const r=this.length-e;if((n===void 0||n>r)&&(n=r),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");let i=!1;for(;;)switch(o){case"hex":return wt(this,t,e,n);case"utf8":case"utf-8":return dt(this,t,e,n);case"ascii":case"latin1":case"binary":return bt(this,t,e,n);case"base64":return mt(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Et(this,t,e,n);default:{if(i)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),i=!0}}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function Bt(t,e,n){return e===0&&n===t.length?P(t):P(t.slice(e,n))}function q(t,e,n){n=Math.min(t.length,n);const o=[];let r=e;for(;r<n;){const i=t[r];let u=null,h=i>239?4:i>223?3:i>191?2:1;if(r+h<=n){let c,l,s,a;switch(h){case 1:{i<128&&(u=i);break}case 2:{c=t[r+1],(c&192)===128&&(a=(i&31)<<6|c&63,a>127&&(u=a));break}case 3:{c=t[r+1],l=t[r+2],(c&192)===128&&(l&192)===128&&(a=(i&15)<<12|(c&63)<<6|l&63,a>2047&&(a<55296||a>57343)&&(u=a));break}case 4:c=t[r+1],l=t[r+2],s=t[r+3],(c&192)===128&&(l&192)===128&&(s&192)===128&&(a=(i&15)<<18|(c&63)<<12|(l&63)<<6|s&63,a>65535&&a<1114112&&(u=a))}}u===null?(u=65533,h=1):u>65535&&(u-=65536,o.push(u>>>10&1023|55296),u=56320|u&1023),o.push(u),r+=h}return It(o)}const W=4096;function It(t){const e=t.length;if(e<=W)return String.fromCharCode.apply(String,t);let n="",o=0;for(;o<e;)n+=String.fromCharCode.apply(String,t.slice(o,o+=W));return n}function At(t,e,n){let o="";n=Math.min(t.length,n);for(let r=e;r<n;++r)o+=String.fromCharCode(t[r]&127);return o}function Ut(t,e,n){let o="";n=Math.min(t.length,n);for(let r=e;r<n;++r)o+=String.fromCharCode(t[r]);return o}function vt(t,e,n){const o=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>o)&&(n=o);let r="";for(let i=e;i<n;++i)r+=_t[t[i]];return r}function Rt(t,e,n){const o=t.slice(e,n);let r="";for(let i=0;i<o.length-1;i+=2)r+=String.fromCharCode(o[i]+o[i+1]*256);return r}f.prototype.slice=function(t,e){const n=this.length;t=Math.trunc(t),e=e===void 0?n:Math.trunc(e),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),e<t&&(e=t);const o=this.subarray(t,e);return Object.setPrototypeOf(o,f.prototype),o};function p(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}f.prototype.readUintLE=f.prototype.readUIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let o=this[t],r=1,i=0;for(;++i<e&&(r*=256);)o+=this[t+i]*r;return o},f.prototype.readUintBE=f.prototype.readUIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let o=this[t+--e],r=1;for(;e>0&&(r*=256);)o+=this[t+--e]*r;return o},f.prototype.readUint8=f.prototype.readUInt8=function(t,e){return t=t>>>0,e||p(t,1,this.length),this[t]},f.prototype.readUint16LE=f.prototype.readUInt16LE=function(t,e){return t=t>>>0,e||p(t,2,this.length),this[t]|this[t+1]<<8},f.prototype.readUint16BE=f.prototype.readUInt16BE=function(t,e){return t=t>>>0,e||p(t,2,this.length),this[t]<<8|this[t+1]},f.prototype.readUint32LE=f.prototype.readUInt32LE=function(t,e){return t=t>>>0,e||p(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216},f.prototype.readUint32BE=f.prototype.readUInt32BE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])},f.prototype.readBigUInt64LE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&v(t,this.length-8);const o=e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24,r=this[++t]+this[++t]*2**8+this[++t]*2**16+n*2**24;return BigInt(o)+(BigInt(r)<<BigInt(32))}),f.prototype.readBigUInt64BE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&v(t,this.length-8);const o=e*2**24+this[++t]*2**16+this[++t]*2**8+this[++t],r=this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n;return(BigInt(o)<<BigInt(32))+BigInt(r)}),f.prototype.readIntLE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let o=this[t],r=1,i=0;for(;++i<e&&(r*=256);)o+=this[t+i]*r;return r*=128,o>=r&&(o-=Math.pow(2,8*e)),o},f.prototype.readIntBE=function(t,e,n){t=t>>>0,e=e>>>0,n||p(t,e,this.length);let o=e,r=1,i=this[t+--o];for(;o>0&&(r*=256);)i+=this[t+--o]*r;return r*=128,i>=r&&(i-=Math.pow(2,8*e)),i},f.prototype.readInt8=function(t,e){return t=t>>>0,e||p(t,1,this.length),this[t]&128?(255-this[t]+1)*-1:this[t]},f.prototype.readInt16LE=function(t,e){t=t>>>0,e||p(t,2,this.length);const n=this[t]|this[t+1]<<8;return n&32768?n|4294901760:n},f.prototype.readInt16BE=function(t,e){t=t>>>0,e||p(t,2,this.length);const n=this[t+1]|this[t]<<8;return n&32768?n|4294901760:n},f.prototype.readInt32LE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},f.prototype.readInt32BE=function(t,e){return t=t>>>0,e||p(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},f.prototype.readBigInt64LE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&v(t,this.length-8);const o=this[t+4]+this[t+5]*2**8+this[t+6]*2**16+(n<<24);return(BigInt(o)<<BigInt(32))+BigInt(e+this[++t]*2**8+this[++t]*2**16+this[++t]*2**24)}),f.prototype.readBigInt64BE=E(function(t){t=t>>>0,U(t,"offset");const e=this[t],n=this[t+7];(e===void 0||n===void 0)&&v(t,this.length-8);const o=(e<<24)+this[++t]*2**16+this[++t]*2**8+this[++t];return(BigInt(o)<<BigInt(32))+BigInt(this[++t]*2**24+this[++t]*2**16+this[++t]*2**8+n)}),f.prototype.readFloatLE=function(t,e){return t=t>>>0,e||p(t,4,this.length),R(this,t,!0,23,4)},f.prototype.readFloatBE=function(t,e){return t=t>>>0,e||p(t,4,this.length),R(this,t,!1,23,4)},f.prototype.readDoubleLE=function(t,e){return t=t>>>0,e||p(t,8,this.length),R(this,t,!0,52,8)},f.prototype.readDoubleBE=function(t,e){return t=t>>>0,e||p(t,8,this.length),R(this,t,!1,52,8)};function g(t,e,n,o,r,i){if(!f.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>r||e<i)throw new RangeError('"value" argument is out of bounds');if(n+o>t.length)throw new RangeError("Index out of range")}f.prototype.writeUintLE=f.prototype.writeUIntLE=function(t,e,n,o){if(t=+t,e=e>>>0,n=n>>>0,!o){const u=Math.pow(2,8*n)-1;g(this,t,e,n,u,0)}let r=1,i=0;for(this[e]=t&255;++i<n&&(r*=256);)this[e+i]=t/r&255;return e+n},f.prototype.writeUintBE=f.prototype.writeUIntBE=function(t,e,n,o){if(t=+t,e=e>>>0,n=n>>>0,!o){const u=Math.pow(2,8*n)-1;g(this,t,e,n,u,0)}let r=n-1,i=1;for(this[e+r]=t&255;--r>=0&&(i*=256);)this[e+r]=t/i&255;return e+n},f.prototype.writeUint8=f.prototype.writeUInt8=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,1,255,0),this[e]=t&255,e+1},f.prototype.writeUint16LE=f.prototype.writeUInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,65535,0),this[e]=t&255,this[e+1]=t>>>8,e+2},f.prototype.writeUint16BE=f.prototype.writeUInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=t&255,e+2},f.prototype.writeUint32LE=f.prototype.writeUInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=t&255,e+4},f.prototype.writeUint32BE=f.prototype.writeUInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4};function J(t,e,n,o,r){tt(e,o,r,t,n,7);let i=Number(e&BigInt(4294967295));t[n++]=i,i=i>>8,t[n++]=i,i=i>>8,t[n++]=i,i=i>>8,t[n++]=i;let u=Number(e>>BigInt(32)&BigInt(4294967295));return t[n++]=u,u=u>>8,t[n++]=u,u=u>>8,t[n++]=u,u=u>>8,t[n++]=u,n}function X(t,e,n,o,r){tt(e,o,r,t,n,7);let i=Number(e&BigInt(4294967295));t[n+7]=i,i=i>>8,t[n+6]=i,i=i>>8,t[n+5]=i,i=i>>8,t[n+4]=i;let u=Number(e>>BigInt(32)&BigInt(4294967295));return t[n+3]=u,u=u>>8,t[n+2]=u,u=u>>8,t[n+1]=u,u=u>>8,t[n]=u,n+8}f.prototype.writeBigUInt64LE=E(function(t,e=0){return J(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),f.prototype.writeBigUInt64BE=E(function(t,e=0){return X(this,t,e,BigInt(0),BigInt("0xffffffffffffffff"))}),f.prototype.writeIntLE=function(t,e,n,o){if(t=+t,e=e>>>0,!o){const h=Math.pow(2,8*n-1);g(this,t,e,n,h-1,-h)}let r=0,i=1,u=0;for(this[e]=t&255;++r<n&&(i*=256);)t<0&&u===0&&this[e+r-1]!==0&&(u=1),this[e+r]=Math.trunc(t/i)-u&255;return e+n},f.prototype.writeIntBE=function(t,e,n,o){if(t=+t,e=e>>>0,!o){const h=Math.pow(2,8*n-1);g(this,t,e,n,h-1,-h)}let r=n-1,i=1,u=0;for(this[e+r]=t&255;--r>=0&&(i*=256);)t<0&&u===0&&this[e+r+1]!==0&&(u=1),this[e+r]=Math.trunc(t/i)-u&255;return e+n},f.prototype.writeInt8=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=t&255,e+1},f.prototype.writeInt16LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,32767,-32768),this[e]=t&255,this[e+1]=t>>>8,e+2},f.prototype.writeInt16BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=t&255,e+2},f.prototype.writeInt32LE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,2147483647,-2147483648),this[e]=t&255,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},f.prototype.writeInt32BE=function(t,e,n){return t=+t,e=e>>>0,n||g(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=t&255,e+4},f.prototype.writeBigInt64LE=E(function(t,e=0){return J(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),f.prototype.writeBigInt64BE=E(function(t,e=0){return X(this,t,e,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function H(t,e,n,o,r,i){if(n+o>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function K(t,e,n,o,r){return e=+e,n=n>>>0,r||H(t,e,n,4),F(t,e,n,o,23,4),n+4}f.prototype.writeFloatLE=function(t,e,n){return K(this,t,e,!0,n)},f.prototype.writeFloatBE=function(t,e,n){return K(this,t,e,!1,n)};function Q(t,e,n,o,r){return e=+e,n=n>>>0,r||H(t,e,n,8),F(t,e,n,o,52,8),n+8}f.prototype.writeDoubleLE=function(t,e,n){return Q(this,t,e,!0,n)},f.prototype.writeDoubleBE=function(t,e,n){return Q(this,t,e,!1,n)},f.prototype.copy=function(t,e,n,o){if(!f.isBuffer(t))throw new TypeError("argument should be a Buffer");if(n||(n=0),!o&&o!==0&&(o=this.length),e>=t.length&&(e=t.length),e||(e=0),o>0&&o<n&&(o=n),o===n||t.length===0||this.length===0)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),t.length-e<o-n&&(o=t.length-e+n);const r=o-n;return this===t&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(e,n,o):Uint8Array.prototype.set.call(t,this.subarray(n,o),e),r},f.prototype.fill=function(t,e,n,o){if(typeof t=="string"){if(typeof e=="string"?(o=e,e=0,n=this.length):typeof n=="string"&&(o=n,n=this.length),o!==void 0&&typeof o!="string")throw new TypeError("encoding must be a string");if(typeof o=="string"&&!f.isEncoding(o))throw new TypeError("Unknown encoding: "+o);if(t.length===1){const i=t.charCodeAt(0);(o==="utf8"&&i<128||o==="latin1")&&(t=i)}}else typeof t=="number"?t=t&255:typeof t=="boolean"&&(t=Number(t));if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e=e>>>0,n=n===void 0?this.length:n>>>0,t||(t=0);let r;if(typeof t=="number")for(r=e;r<n;++r)this[r]=t;else{const i=f.isBuffer(t)?t:f.from(t,o),u=i.length;if(u===0)throw new TypeError('The value "'+t+'" is invalid for argument "value"');for(r=0;r<n-e;++r)this[r+e]=i[r%u]}return this};const A={};function C(t,e,n){A[t]=class extends n{constructor(){super(),Object.defineProperty(this,"message",{value:Reflect.apply(e,this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${t}]`,this.stack,delete this.name}get code(){return t}set code(o){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:o,writable:!0})}toString(){return`${this.name} [${t}]: ${this.message}`}}}C("ERR_BUFFER_OUT_OF_BOUNDS",function(t){return t?`${t} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),C("ERR_INVALID_ARG_TYPE",function(t,e){return`The "${t}" argument must be of type number. Received type ${typeof e}`},TypeError),C("ERR_OUT_OF_RANGE",function(t,e,n){let o=`The value of "${t}" is out of range.`,r=n;return Number.isInteger(n)&&Math.abs(n)>2**32?r=Z(String(n)):typeof n=="bigint"&&(r=String(n),(n>BigInt(2)**BigInt(32)||n<-(BigInt(2)**BigInt(32)))&&(r=Z(r)),r+="n"),o+=` It must be ${e}. Received ${r}`,o},RangeError);function Z(t){let e="",n=t.length;const o=t[0]==="-"?1:0;for(;n>=o+4;n-=3)e=`_${t.slice(n-3,n)}${e}`;return`${t.slice(0,n)}${e}`}function Tt(t,e,n){U(e,"offset"),(t[e]===void 0||t[e+n]===void 0)&&v(e,t.length-(n+1))}function tt(t,e,n,o,r,i){if(t>n||t<e){const u=typeof e=="bigint"?"n":"";let h;throw h=e===0||e===BigInt(0)?`>= 0${u} and < 2${u} ** ${(i+1)*8}${u}`:`>= -(2${u} ** ${(i+1)*8-1}${u}) and < 2 ** ${(i+1)*8-1}${u}`,new A.ERR_OUT_OF_RANGE("value",h,t)}Tt(o,r,i)}function U(t,e){if(typeof t!="number")throw new A.ERR_INVALID_ARG_TYPE(e,"number",t)}function v(t,e,n){throw Math.floor(t)!==t?(U(t,n),new A.ERR_OUT_OF_RANGE("offset","an integer",t)):e<0?new A.ERR_BUFFER_OUT_OF_BOUNDS:new A.ERR_OUT_OF_RANGE("offset",`>= 0 and <= ${e}`,t)}const Ot=/[^\w+/-]/g;function Lt(t){if(t=t.split("=")[0],t=t.trim().replace(Ot,""),t.length<2)return"";for(;t.length%4!==0;)t=t+"=";return t}function $(t,e){e=e||Number.POSITIVE_INFINITY;let n;const o=t.length;let r=null;const i=[];for(let u=0;u<o;++u){if(n=t.charCodeAt(u),n>55295&&n<57344){if(!r){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}else if(u+1===o){(e-=3)>-1&&i.push(239,191,189);continue}r=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),r=n;continue}n=(r-55296<<10|n-56320)+65536}else r&&(e-=3)>-1&&i.push(239,191,189);if(r=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,n&63|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,n&63|128)}else if(n<1114112){if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,n&63|128)}else throw new Error("Invalid code point")}return i}function Nt(t){const e=[];for(let n=0;n<t.length;++n)e.push(t.charCodeAt(n)&255);return e}function St(t,e){let n,o,r;const i=[];for(let u=0;u<t.length&&!((e-=2)<0);++u)n=t.charCodeAt(u),o=n>>8,r=n%256,i.push(r,o);return i}function et(t){return ft(Lt(t))}function T(t,e,n,o){let r;for(r=0;r<o&&!(r+n>=e.length||r>=t.length);++r)e[r+n]=t[r];return r}function b(t,e){return t instanceof e||t!=null&&t.constructor!=null&&t.constructor.name!=null&&t.constructor.name===e.name}function M(t){return t!==t}const _t=function(){const t="0123456789abcdef",e=Array.from({length:256});for(let n=0;n<16;++n){const o=n*16;for(let r=0;r<16;++r)e[o+r]=t[n]+t[r]}return e}();function E(t){return typeof BigInt>"u"?xt:t}function xt(){throw new Error("BigInt not supported")}export{f as Buffer};
