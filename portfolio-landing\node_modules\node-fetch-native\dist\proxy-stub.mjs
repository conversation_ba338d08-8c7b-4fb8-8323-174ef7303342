var o=Object.defineProperty;var e=(t,r)=>o(t,"name",{value:r,configurable:!0});var f=Object.defineProperty,c=e((t,r)=>f(t,"name",{value:r,configurable:!0}),"e");function n(){return{agent:void 0,dispatcher:void 0}}e(n,"createProxy"),c(n,"createProxy");function a(){return globalThis.fetch}e(a,"createFetch"),c(a,"createFetch");const i=globalThis.fetch;export{a as createFetch,n as createProxy,i as fetch};
