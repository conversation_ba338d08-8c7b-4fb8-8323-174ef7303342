let y=10;const Y=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),F=(e,t)=>e,_=Error,Z=Error,h=Error,w=Error,tt=Error,R=Symbol.for("nodejs.rejection"),f=Symbol.for("kCapture"),M=Symbol.for("events.errorMonitor"),g=Symbol.for("shapeMode"),b=Symbol.for("events.maxEventTargetListeners"),et=Symbol.for("kEnhanceStackBeforeInspector"),rt=Symbol.for("nodejs.watermarkData"),v=Symbol.for("kAsyncResource"),nt=Symbol.for("kFirstEventParam"),S=Symbol.for("kResistStopPropagation"),W=Symbol.for("events.maxEventTargetListenersWarned");class m{_events=void 0;_eventsCount=0;_maxListeners=y;[f]=!1;[g]=!1;static captureRejectionSymbol=R;static errorMonitor=M;static kMaxEventTargetListeners=b;static kMaxEventTargetListenersWarned=W;static usingDomains=!1;static get on(){return it}static get once(){return ot}static get getEventListeners(){return ct}static get getMaxListeners(){return ut}static get addAbortListener(){return $}static get EventEmitterAsyncResource(){return st}static get EventEmitter(){return m}static setMaxListeners(t=y,...r){if(r.length===0)y=t;else for(const n of r)if(q(n))n[b]=t,n[W]=!1;else if(typeof n.setMaxListeners=="function")n.setMaxListeners(t);else throw new h("eventTargets",["EventEmitter","EventTarget"],n)}static listenerCount(t,r){if(typeof t.listenerCount=="function")return t.listenerCount(r);m.prototype.listenerCount.call(t,r)}static init(){throw new Error("EventEmitter.init() is not implemented.")}static get captureRejections(){return this[f]}static set captureRejections(t){this[f]=t}static get defaultMaxListeners(){return y}static set defaultMaxListeners(t){y=t}constructor(t){this._events===void 0||this._events===Object.getPrototypeOf(this)._events?(this._events={__proto__:null},this._eventsCount=0,this[g]=!1):this[g]=!0,this._maxListeners=this._maxListeners||void 0,t?.captureRejections?this[f]=!!t.captureRejections:this[f]=m.prototype[f]}setMaxListeners(t){return this._maxListeners=t,this}getMaxListeners(){return j(this)}emit(t,...r){let n=t==="error";const s=this._events;if(s!==void 0)n&&s[M]!==void 0&&this.emit(M,...r),n=n&&s.error===void 0;else if(!n)return!1;if(n){let i;if(r.length>0&&(i=r[0]),i instanceof Error){try{const a={};Error.captureStackTrace?.(a,m.prototype.emit),Object.defineProperty(i,et,{__proto__:null,value:Function.prototype.bind(at,this,i,a),configurable:!0})}catch{}throw i}let u;try{u=F(i)}catch{u=i}const l=new Z(u);throw l.context=i,l}const o=s[t];if(o===void 0)return!1;if(typeof o=="function"){const i=o.apply(this,r);i!=null&&z(this,i,t,r)}else{const i=o.length,u=C(o);for(let l=0;l<i;++l){const a=u[l].apply(this,r);a!=null&&z(this,a,t,r)}}return!0}addListener(t,r){return B(this,t,r,!1),this}on(t,r){return this.addListener(t,r)}prependListener(t,r){return B(this,t,r,!0),this}once(t,r){return this.on(t,G(this,t,r)),this}prependOnceListener(t,r){return this.prependListener(t,G(this,t,r)),this}removeListener(t,r){const n=this._events;if(n===void 0)return this;const s=n[t];if(s===void 0)return this;if(s===r||s.listener===r)this._eventsCount-=1,this[g]?n[t]=void 0:this._eventsCount===0?this._events={__proto__:null}:(delete n[t],n.removeListener&&this.emit("removeListener",t,s.listener||r));else if(typeof s!="function"){let o=-1;for(let i=s.length-1;i>=0;i--)if(s[i]===r||s[i].listener===r){o=i;break}if(o<0)return this;o===0?s.shift():mt(s,o),s.length===1&&(n[t]=s[0]),n.removeListener!==void 0&&this.emit("removeListener",t,r)}return this}off(t,r){return this.removeListener(t,r)}removeAllListeners(t){const r=this._events;if(r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events={__proto__:null},this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events={__proto__:null}:delete r[t]),this[g]=!1,this;if(arguments.length===0){for(const s of Reflect.ownKeys(r))s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events={__proto__:null},this._eventsCount=0,this[g]=!1,this}const n=r[t];if(typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(let s=n.length-1;s>=0;s--)this.removeListener(t,n[s]);return this}listeners(t){return U(this,t,!0)}rawListeners(t){return U(this,t,!1)}eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}listenerCount(t,r){const n=this._events;if(n!==void 0){const s=n[t];if(typeof s=="function")return r!=null?r===s||r===s.listener?1:0:1;if(s!==void 0){if(r!=null){let o=0;for(let i=0,u=s.length;i<u;i++)(s[i]===r||s[i].listener===r)&&o++;return o}return s.length}}return 0}}class st extends m{constructor(t){let r;typeof t=="string"?(r=t,t=void 0):r=t?.name||new.target.name,super(t),this[v]=new EventEmitterReferencingAsyncResource(this,r,t)}emit(t,...r){if(this[v]===void 0)throw new _("EventEmitterAsyncResource");const{asyncResource:n}=this;return Array.prototype.unshift(r,super.emit,this,t),Reflect.apply(n.runInAsyncScope,n,r)}emitDestroy(){if(this[v]===void 0)throw new _("EventEmitterAsyncResource");this.asyncResource.emitDestroy()}get asyncId(){if(this[v]===void 0)throw new _("EventEmitterAsyncResource");return this.asyncResource.asyncId()}get triggerAsyncId(){if(this[v]===void 0)throw new _("EventEmitterAsyncResource");return this.asyncResource.triggerAsyncId()}get asyncResource(){if(this[v]===void 0)throw new _("EventEmitterAsyncResource");return this[v]}}const it=function(e,t,r={}){const n=r.signal;if(n?.aborted)throw new w(void 0,{cause:n?.reason});const s=r.highWaterMark??r.highWatermark??Number.MAX_SAFE_INTEGER,o=r.lowWaterMark??r.lowWatermark??1,i=new K,u=new K;let l=!1,a=null,p=!1,d=0;const H=Object.setPrototypeOf({next(){if(d){const c=i.shift();return d--,l&&d<o&&(e.resume?.(),l=!1),Promise.resolve(P(c,!1))}if(a){const c=Promise.reject(a);return a=null,c}return p?L():new Promise(function(c,X){u.push({resolve:c,reject:X})})},return(){return L()},throw(c){if(!c||!(c instanceof Error))throw new h("EventEmitter.AsyncIterator","Error",c);A(c)},[Symbol.asyncIterator](){return this},[rt]:{get size(){return d},get low(){return o},get high(){return s},get isPaused(){return l}}},Y),{addEventListener:x,removeAll:J}=vt();x(e,t,r[nt]?I:function(...c){return I(c)}),t!=="error"&&typeof e.on=="function"&&x(e,"error",A);const O=r?.close;if(O?.length)for(const c of O)x(e,c,L);const Q=n?$(n,V):null;return H;function V(){A(new w(void 0,{cause:n?.reason}))}function I(c){u.isEmpty()?(d++,!l&&d>s&&(l=!0,e.pause?.()),i.push(c)):u.shift().resolve(P(c,!1))}function A(c){u.isEmpty()?a=c:u.shift().reject(c),L()}function L(){Q?.[Symbol.dispose](),J(),p=!0;const c=P(void 0,!0);for(;!u.isEmpty();)u.shift().resolve(c);return Promise.resolve(c)}},ot=async function(e,t,r={}){const n=r?.signal;if(n?.aborted)throw new w(void 0,{cause:n?.reason});return new Promise((s,o)=>{const i=p=>{typeof e.removeListener=="function"&&e.removeListener(t,u),n!=null&&E(n,"abort",a),o(p)},u=(...p)=>{typeof e.removeListener=="function"&&e.removeListener("error",i),n!=null&&E(n,"abort",a),s(p)},l={__proto__:null,once:!0,[S]:!0};T(e,t,u,l),t!=="error"&&typeof e.once=="function"&&e.once("error",i);function a(){E(e,t,u),E(e,"error",i),o(new w(void 0,{cause:n?.reason}))}n!=null&&T(n,"abort",a,{__proto__:null,once:!0,[S]:!0})})},$=function(e,t){if(e===void 0)throw new h("signal","AbortSignal",e);let r;return e.aborted?queueMicrotask(()=>t()):(e.addEventListener("abort",t,{__proto__:null,once:!0,[S]:!0}),r=()=>{e.removeEventListener("abort",t)}),{__proto__:null,[Symbol.dispose](){r?.()}}},ct=function(e,t){if(typeof e.listeners=="function")return e.listeners(t);if(q(e)){const r=e[kEvents].get(t),n=[];let s=r?.next;for(;s?.listener!==void 0;){const o=s.listener?.deref?s.listener.deref():s.listener;n.push(o),s=s.next}return n}throw new h("emitter",["EventEmitter","EventTarget"],e)},ut=function(e){if(typeof e?.getMaxListeners=="function")return j(e);if(e?.[b])return e[b];throw new h("emitter",["EventEmitter","EventTarget"],e)},D=2048,k=D-1;class N{bottom;top;list;next;constructor(){this.bottom=0,this.top=0,this.list=new Array(D),this.next=null}isEmpty(){return this.top===this.bottom}isFull(){return(this.top+1&k)===this.bottom}push(t){this.list[this.top]=t,this.top=this.top+1&k}shift(){const t=this.list[this.bottom];return t===void 0?null:(this.list[this.bottom]=void 0,this.bottom=this.bottom+1&k,t)}}class K{head;tail;constructor(){this.head=this.tail=new N}isEmpty(){return this.head.isEmpty()}push(t){this.head.isFull()&&(this.head=this.head.next=new N),this.head.push(t)}shift(){const t=this.tail,r=t.shift();return t.isEmpty()&&t.next!==null&&(this.tail=t.next,t.next=null),r}}function q(e){return typeof e?.addEventListener=="function"}function z(e,t,r,n){if(e[f])try{const s=t.then;typeof s=="function"&&s.call(t,void 0,function(o){process.nextTick(lt,e,o,r,n)})}catch(s){e.emit("error",s)}}function lt(e,t,r,n){if(typeof e[R]=="function")e[R](t,r,...n);else{const s=e[f];try{e[f]=!1,e.emit("error",t)}finally{e[f]=s}}}function j(e){return e._maxListeners===void 0?y:e._maxListeners}function at(e,t){let r="";try{const{name:o}=this.constructor;o!=="EventEmitter"&&(r=` on ${o} instance`)}catch{}const n=`
Emitted 'error' event${r} at:
`,s=(t.stack||"").split(`
`).slice(1);return e.stack+n+s.join(`
`)}function B(e,t,r,n){let s,o,i;if(o=e._events,o===void 0?(o=e._events={__proto__:null},e._eventsCount=0):(o.newListener!==void 0&&(e.emit("newListener",t,r.listener??r),o=e._events),i=o[t]),i===void 0)o[t]=r,++e._eventsCount;else if(typeof i=="function"?i=o[t]=n?[r,i]:[i,r]:n?i.unshift(r):i.push(r),s=j(e),s>0&&i.length>s&&!i.warned){i.warned=!0;const u=new tt(`Possible EventEmitter memory leak detected. ${i.length} ${String(t)} listeners added to ${F(e)}. MaxListeners is ${s}. Use emitter.setMaxListeners() to increase limit`,{name:"MaxListenersExceededWarning",emitter:e,type:t,count:i.length});process.emitWarning(u)}return e}function ft(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function G(e,t,r){const n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},s=ft.bind(n);return s.listener=r,n.wrapFn=s,s}function U(e,t,r){const n=e._events;if(n===void 0)return[];const s=n[t];return s===void 0?[]:typeof s=="function"?r?[s.listener||s]:[s]:r?ht(s):C(s)}function C(e){switch(e.length){case 2:return[e[0],e[1]];case 3:return[e[0],e[1],e[2]];case 4:return[e[0],e[1],e[2],e[3]];case 5:return[e[0],e[1],e[2],e[3],e[4]];case 6:return[e[0],e[1],e[2],e[3],e[4],e[5]]}return Array.prototype.slice(e)}function ht(e){const t=C(e);for(let r=0;r<t.length;++r){const n=t[r].listener;typeof n=="function"&&(t[r]=n)}return t}function P(e,t){return{value:e,done:t}}function E(e,t,r,n){if(typeof e.removeListener=="function")e.removeListener(t,r);else if(typeof e.removeEventListener=="function")e.removeEventListener(t,r,n);else throw new h("emitter","EventEmitter",e)}function T(e,t,r,n){if(typeof e.on=="function")n?.once?e.once(t,r):e.on(t,r);else if(typeof e.addEventListener=="function")e.addEventListener(t,r,n);else throw new h("emitter","EventEmitter",e)}function vt(){const e=[];return{addEventListener(t,r,n,s){T(t,r,n,s),Array.prototype.push(e,[t,r,n,s])},removeAll(){for(;e.length>0;)Reflect.apply(E,void 0,e.pop())}}}function mt(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}export{m as EventEmitter};
