"use strict";let y=10;const Y=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype),F=(e,t)=>e,g=Error,J=Error,h=Error,b=Error,Z=Error,R=Symbol.for("nodejs.rejection"),l=Symbol.for("kCapture"),A=Symbol.for("events.errorMonitor"),p=Symbol.for("shapeMode"),w=Symbol.for("events.maxEventTargetListeners"),ee=Symbol.for("kEnhanceStackBeforeInspector"),te=Symbol.for("nodejs.watermarkData"),E=Symbol.for("kAsyncResource"),ne=Symbol.for("kFirstEventParam"),N=Symbol.for("kResistStopPropagation"),W=Symbol.for("events.maxEventTargetListenersWarned");class EventEmitter{_events=void 0;_eventsCount=0;_maxListeners=y;[l]=!1;[p]=!1;static captureRejectionSymbol=R;static errorMonitor=A;static kMaxEventTargetListeners=w;static kMaxEventTargetListenersWarned=W;static usingDomains=!1;static get on(){return on}static get once(){return once}static get getEventListeners(){return getEventListeners}static get getMaxListeners(){return getMaxListeners}static get addAbortListener(){return addAbortListener}static get EventEmitterAsyncResource(){return EventEmitterAsyncResource}static get EventEmitter(){return EventEmitter}static setMaxListeners(t=y,...r){if(r.length===0)y=t;else for(const n of r)if(H(n))n[w]=t,n[W]=!1;else if(typeof n.setMaxListeners=="function")n.setMaxListeners(t);else throw new h("eventTargets",["EventEmitter","EventTarget"],n)}static listenerCount(t,r){if(typeof t.listenerCount=="function")return t.listenerCount(r);EventEmitter.prototype.listenerCount.call(t,r)}static init(){throw new Error("EventEmitter.init() is not implemented.")}static get captureRejections(){return this[l]}static set captureRejections(t){this[l]=t}static get defaultMaxListeners(){return y}static set defaultMaxListeners(t){y=t}constructor(t){this._events===void 0||this._events===Object.getPrototypeOf(this)._events?(this._events={__proto__:null},this._eventsCount=0,this[p]=!1):this[p]=!0,this._maxListeners=this._maxListeners||void 0,t?.captureRejections?this[l]=!!t.captureRejections:this[l]=EventEmitter.prototype[l]}setMaxListeners(t){return this._maxListeners=t,this}getMaxListeners(){return S(this)}emit(t,...r){let n=t==="error";const s=this._events;if(s!==void 0)n&&s[A]!==void 0&&this.emit(A,...r),n=n&&s.error===void 0;else if(!n)return!1;if(n){let i;if(r.length>0&&(i=r[0]),i instanceof Error){try{const f={};Error.captureStackTrace?.(f,EventEmitter.prototype.emit),Object.defineProperty(i,ee,{__proto__:null,value:Function.prototype.bind(re,this,i,f),configurable:!0})}catch{}throw i}let u;try{u=F(i)}catch{u=i}const a=new J(u);throw a.context=i,a}const o=s[t];if(o===void 0)return!1;if(typeof o=="function"){const i=o.apply(this,r);i!=null&&O(this,i,t,r)}else{const i=o.length,u=C(o);for(let a=0;a<i;++a){const f=u[a].apply(this,r);f!=null&&O(this,f,t,r)}}return!0}addListener(t,r){return z(this,t,r,!1),this}on(t,r){return this.addListener(t,r)}prependListener(t,r){return z(this,t,r,!0),this}once(t,r){return this.on(t,U(this,t,r)),this}prependOnceListener(t,r){return this.prependListener(t,U(this,t,r)),this}removeListener(t,r){const n=this._events;if(n===void 0)return this;const s=n[t];if(s===void 0)return this;if(s===r||s.listener===r)this._eventsCount-=1,this[p]?n[t]=void 0:this._eventsCount===0?this._events={__proto__:null}:(delete n[t],n.removeListener&&this.emit("removeListener",t,s.listener||r));else if(typeof s!="function"){let o=-1;for(let i=s.length-1;i>=0;i--)if(s[i]===r||s[i].listener===r){o=i;break}if(o<0)return this;o===0?s.shift():ce(s,o),s.length===1&&(n[t]=s[0]),n.removeListener!==void 0&&this.emit("removeListener",t,r)}return this}off(t,r){return this.removeListener(t,r)}removeAllListeners(t){const r=this._events;if(r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events={__proto__:null},this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events={__proto__:null}:delete r[t]),this[p]=!1,this;if(arguments.length===0){for(const s of Reflect.ownKeys(r))s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events={__proto__:null},this._eventsCount=0,this[p]=!1,this}const n=r[t];if(typeof n=="function")this.removeListener(t,n);else if(n!==void 0)for(let s=n.length-1;s>=0;s--)this.removeListener(t,n[s]);return this}listeners(t){return G(this,t,!0)}rawListeners(t){return G(this,t,!1)}eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}listenerCount(t,r){const n=this._events;if(n!==void 0){const s=n[t];if(typeof s=="function")return r!=null?r===s||r===s.listener?1:0:1;if(s!==void 0){if(r!=null){let o=0;for(let i=0,u=s.length;i<u;i++)(s[i]===r||s[i].listener===r)&&o++;return o}return s.length}}return 0}}class EventEmitterAsyncResource extends EventEmitter{constructor(t){let r;typeof t=="string"?(r=t,t=void 0):r=t?.name||new.target.name,super(t),this[E]=new EventEmitterReferencingAsyncResource(this,r,t)}emit(t,...r){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");const{asyncResource:n}=this;return Array.prototype.unshift(r,super.emit,this,t),Reflect.apply(n.runInAsyncScope,n,r)}emitDestroy(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");this.asyncResource.emitDestroy()}get asyncId(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this.asyncResource.asyncId()}get triggerAsyncId(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this.asyncResource.triggerAsyncId()}get asyncResource(){if(this[E]===void 0)throw new g("EventEmitterAsyncResource");return this[E]}}const on=function(e,t,r={}){const n=r.signal;if(n?.aborted)throw new b(void 0,{cause:n?.reason});const s=r.highWaterMark??r.highWatermark??Number.MAX_SAFE_INTEGER,o=r.lowWaterMark??r.lowWatermark??1,i=new $,u=new $;let a=!1,f=null,v=!1,m=0;const I=Object.setPrototypeOf({next(){if(m){const c=i.shift();return m--,a&&m<o&&(e.resume?.(),a=!1),Promise.resolve(T(c,!1))}if(f){const c=Promise.reject(f);return f=null,c}return v?d():new Promise(function(c,V){u.push({resolve:c,reject:V})})},return(){return d()},throw(c){if(!c||!(c instanceof Error))throw new h("EventEmitter.AsyncIterator","Error",c);x(c)},[Symbol.asyncIterator](){return this},[te]:{get size(){return m},get low(){return o},get high(){return s},get isPaused(){return a}}},Y),{addEventListener:_,removeAll:K}=ue();_(e,t,r[ne]?j:function(...c){return j(c)}),t!=="error"&&typeof e.on=="function"&&_(e,"error",x);const k=r?.close;if(k?.length)for(const c of k)_(e,c,d);const q=n?addAbortListener(n,Q):null;return I;function Q(){x(new b(void 0,{cause:n?.reason}))}function j(c){u.isEmpty()?(m++,!a&&m>s&&(a=!0,e.pause?.()),i.push(c)):u.shift().resolve(T(c,!1))}function x(c){u.isEmpty()?f=c:u.shift().reject(c),d()}function d(){q?.[Symbol.dispose](),K(),v=!0;const c=T(void 0,!0);for(;!u.isEmpty();)u.shift().resolve(c);return Promise.resolve(c)}},once=async function(e,t,r={}){const n=r?.signal;if(n?.aborted)throw new b(void 0,{cause:n?.reason});return new Promise((s,o)=>{const i=v=>{typeof e.removeListener=="function"&&e.removeListener(t,u),n!=null&&L(n,"abort",f),o(v)},u=(...v)=>{typeof e.removeListener=="function"&&e.removeListener("error",i),n!=null&&L(n,"abort",f),s(v)},a={__proto__:null,once:!0,[N]:!0};P(e,t,u,a),t!=="error"&&typeof e.once=="function"&&e.once("error",i);function f(){L(e,t,u),L(e,"error",i),o(new b(void 0,{cause:n?.reason}))}n!=null&&P(n,"abort",f,{__proto__:null,once:!0,[N]:!0})})},addAbortListener=function(e,t){if(e===void 0)throw new h("signal","AbortSignal",e);let r;return e.aborted?queueMicrotask(()=>t()):(e.addEventListener("abort",t,{__proto__:null,once:!0,[N]:!0}),r=()=>{e.removeEventListener("abort",t)}),{__proto__:null,[Symbol.dispose](){r?.()}}},getEventListeners=function(e,t){if(typeof e.listeners=="function")return e.listeners(t);if(H(e)){const r=e[kEvents].get(t),n=[];let s=r?.next;for(;s?.listener!==void 0;){const o=s.listener?.deref?s.listener.deref():s.listener;n.push(o),s=s.next}return n}throw new h("emitter",["EventEmitter","EventTarget"],e)},getMaxListeners=function(e){if(typeof e?.getMaxListeners=="function")return S(e);if(e?.[w])return e[w];throw new h("emitter",["EventEmitter","EventTarget"],e)},D=2048,M=D-1;class B{bottom;top;list;next;constructor(){this.bottom=0,this.top=0,this.list=new Array(D),this.next=null}isEmpty(){return this.top===this.bottom}isFull(){return(this.top+1&M)===this.bottom}push(t){this.list[this.top]=t,this.top=this.top+1&M}shift(){const t=this.list[this.bottom];return t===void 0?null:(this.list[this.bottom]=void 0,this.bottom=this.bottom+1&M,t)}}class ${head;tail;constructor(){this.head=this.tail=new B}isEmpty(){return this.head.isEmpty()}push(t){this.head.isFull()&&(this.head=this.head.next=new B),this.head.push(t)}shift(){const t=this.tail,r=t.shift();return t.isEmpty()&&t.next!==null&&(this.tail=t.next,t.next=null),r}}function H(e){return typeof e?.addEventListener=="function"}function O(e,t,r,n){if(e[l])try{const s=t.then;typeof s=="function"&&s.call(t,void 0,function(o){process.nextTick(se,e,o,r,n)})}catch(s){e.emit("error",s)}}function se(e,t,r,n){if(typeof e[R]=="function")e[R](t,r,...n);else{const s=e[l];try{e[l]=!1,e.emit("error",t)}finally{e[l]=s}}}function S(e){return e._maxListeners===void 0?y:e._maxListeners}function re(e,t){let r="";try{const{name:o}=this.constructor;o!=="EventEmitter"&&(r=` on ${o} instance`)}catch{}const n=`
Emitted 'error' event${r} at:
`,s=(t.stack||"").split(`
`).slice(1);return e.stack+n+s.join(`
`)}function z(e,t,r,n){let s,o,i;if(o=e._events,o===void 0?(o=e._events={__proto__:null},e._eventsCount=0):(o.newListener!==void 0&&(e.emit("newListener",t,r.listener??r),o=e._events),i=o[t]),i===void 0)o[t]=r,++e._eventsCount;else if(typeof i=="function"?i=o[t]=n?[r,i]:[i,r]:n?i.unshift(r):i.push(r),s=S(e),s>0&&i.length>s&&!i.warned){i.warned=!0;const u=new Z(`Possible EventEmitter memory leak detected. ${i.length} ${String(t)} listeners added to ${F(e)}. MaxListeners is ${s}. Use emitter.setMaxListeners() to increase limit`,{name:"MaxListenersExceededWarning",emitter:e,type:t,count:i.length});process.emitWarning(u)}return e}function ie(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function U(e,t,r){const n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},s=ie.bind(n);return s.listener=r,n.wrapFn=s,s}function G(e,t,r){const n=e._events;if(n===void 0)return[];const s=n[t];return s===void 0?[]:typeof s=="function"?r?[s.listener||s]:[s]:r?oe(s):C(s)}function C(e){switch(e.length){case 2:return[e[0],e[1]];case 3:return[e[0],e[1],e[2]];case 4:return[e[0],e[1],e[2],e[3]];case 5:return[e[0],e[1],e[2],e[3],e[4]];case 6:return[e[0],e[1],e[2],e[3],e[4],e[5]]}return Array.prototype.slice(e)}function oe(e){const t=C(e);for(let r=0;r<t.length;++r){const n=t[r].listener;typeof n=="function"&&(t[r]=n)}return t}function T(e,t){return{value:e,done:t}}function L(e,t,r,n){if(typeof e.removeListener=="function")e.removeListener(t,r);else if(typeof e.removeEventListener=="function")e.removeEventListener(t,r,n);else throw new h("emitter","EventEmitter",e)}function P(e,t,r,n){if(typeof e.on=="function")n?.once?e.once(t,r):e.on(t,r);else if(typeof e.addEventListener=="function")e.addEventListener(t,r,n);else throw new h("emitter","EventEmitter",e)}function ue(){const e=[];return{addEventListener(t,r,n,s){P(t,r,n,s),Array.prototype.push(e,[t,r,n,s])},removeAll(){for(;e.length>0;)Reflect.apply(L,void 0,e.pop())}}}function ce(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}exports.EventEmitter=EventEmitter;
