import{EventEmitter as f}from"node-mock-http/_polyfill/events";import{Buffer as h}from"node-mock-http/_polyfill/buffer";function o(n){throw new Error(`${n} is not implemented yet!`)}class i extends f{__unenv__={};readableEncoding=null;readableEnded=!0;readableFlowing=!1;readableHighWaterMark=0;readableLength=0;readableObjectMode=!1;readableAborted=!1;readableDidRead=!1;closed=!1;errored=null;readable=!1;destroyed=!1;static from(e,t){return new i(t)}constructor(e){super()}_read(e){}read(e){}setEncoding(e){return this}pause(){return this}resume(){return this}isPaused(){return!0}unpipe(e){return this}unshift(e,t){}wrap(e){return this}push(e,t){return!1}_destroy(e,t){this.removeAllListeners()}destroy(e){return this.destroyed=!0,this._destroy(e),this}pipe(e,t){return{}}compose(e,t){throw new Error("Method not implemented.")}[Symbol.asyncDispose](){return this.destroy(),Promise.resolve()}async*[Symbol.asyncIterator](){throw o("Readable.asyncIterator")}iterator(e){throw o("Readable.iterator")}map(e,t){throw o("Readable.map")}filter(e,t){throw o("Readable.filter")}forEach(e,t){throw o("Readable.forEach")}reduce(e,t,r){throw o("Readable.reduce")}find(e,t){throw o("Readable.find")}findIndex(e,t){throw o("Readable.findIndex")}some(e,t){throw o("Readable.some")}toArray(e){throw o("Readable.toArray")}every(e,t){throw o("Readable.every")}flatMap(e,t){throw o("Readable.flatMap")}drop(e,t){throw o("Readable.drop")}take(e,t){throw o("Readable.take")}asIndexedPairs(e){throw o("Readable.asIndexedPairs")}}class l extends f{__unenv__={};writable=!0;writableEnded=!1;writableFinished=!1;writableHighWaterMark=0;writableLength=0;writableObjectMode=!1;writableCorked=0;closed=!1;errored=null;writableNeedDrain=!1;writableAborted=!1;destroyed=!1;_data;_encoding="utf8";constructor(e){super()}pipe(e,t){return{}}_write(e,t,r){if(this.writableEnded){r&&r();return}if(this._data===void 0)this._data=e;else{const s=typeof this._data=="string"?h.from(this._data,this._encoding||t||"utf8"):this._data,a=typeof e=="string"?h.from(e,t||this._encoding||"utf8"):e;this._data=h.concat([s,a])}this._encoding=t,r&&r()}_writev(e,t){}_destroy(e,t){}_final(e){}write(e,t,r){const s=typeof t=="string"?this._encoding:"utf8",a=typeof t=="function"?t:typeof r=="function"?r:void 0;return this._write(e,s,a),!0}setDefaultEncoding(e){return this}end(e,t,r){const s=typeof e=="function"?e:typeof t=="function"?t:typeof r=="function"?r:void 0;if(this.writableEnded)return s&&s(),this;const a=e===s?void 0:e;if(a){const u=t===s?void 0:t;this.write(a,u,s)}return this.writableEnded=!0,this.writableFinished=!0,this.emit("close"),this.emit("finish"),this}cork(){}uncork(){}destroy(e){return this.destroyed=!0,delete this._data,this.removeAllListeners(),this}compose(e,t){throw new Error("Method not implemented.")}}const c=class{allowHalfOpen=!0;_destroy;constructor(e=new i,t=new l){Object.assign(this,e),Object.assign(this,t),this._destroy=g(e._destroy,t._destroy)}};function _(){return Object.assign(c.prototype,i.prototype),Object.assign(c.prototype,l.prototype),c}function g(...n){return function(...e){for(const t of n)t(...e)}}const m=_();class A extends m{__unenv__={};bufferSize=0;bytesRead=0;bytesWritten=0;connecting=!1;destroyed=!1;pending=!1;localAddress="";localPort=0;remoteAddress="";remoteFamily="";remotePort=0;autoSelectFamilyAttemptedAddresses=[];readyState="readOnly";constructor(e){super()}write(e,t,r){return!1}connect(e,t,r){return this}end(e,t,r){return this}setEncoding(e){return this}pause(){return this}resume(){return this}setTimeout(e,t){return this}setNoDelay(e){return this}setKeepAlive(e,t){return this}address(){return{}}unref(){return this}ref(){return this}destroySoon(){this.destroy()}resetAndDestroy(){const e=new Error("ERR_SOCKET_CLOSED");return e.code="ERR_SOCKET_CLOSED",this.destroy(e),this}}class y extends i{aborted=!1;httpVersion="1.1";httpVersionMajor=1;httpVersionMinor=1;complete=!0;connection;socket;headers={};trailers={};method="GET";url="/";statusCode=200;statusMessage="";closed=!1;errored=null;readable=!1;constructor(e){super(),this.socket=this.connection=e||new A}get rawHeaders(){const e=this.headers,t=[];for(const r in e)if(Array.isArray(e[r]))for(const s of e[r])t.push(r,s);else t.push(r,e[r]);return t}get rawTrailers(){return[]}setTimeout(e,t){return this}get headersDistinct(){return p(this.headers)}get trailersDistinct(){return p(this.trailers)}}function p(n){const e={};for(const[t,r]of Object.entries(n))t&&(e[t]=(Array.isArray(r)?r:[r]).filter(Boolean));return e}class w extends l{statusCode=200;statusMessage="";upgrading=!1;chunkedEncoding=!1;shouldKeepAlive=!1;useChunkedEncodingByDefault=!1;sendDate=!1;finished=!1;headersSent=!1;strictContentLength=!1;connection=null;socket=null;req;_headers={};constructor(e){super(),this.req=e}assignSocket(e){e._httpMessage=this,this.socket=e,this.connection=e,this.emit("socket",e),this._flush()}_flush(){this.flushHeaders()}detachSocket(e){}writeContinue(e){}writeHead(e,t,r){e&&(this.statusCode=e),typeof t=="string"&&(this.statusMessage=t,t=void 0);const s=r||t;if(s&&!Array.isArray(s))for(const a in s)this.setHeader(a,s[a]);return this.headersSent=!0,this}writeProcessing(){}setTimeout(e,t){return this}appendHeader(e,t){e=e.toLowerCase();const r=this._headers[e],s=[...Array.isArray(r)?r:[r],...Array.isArray(t)?t:[t]].filter(Boolean);return this._headers[e]=s.length>1?s:s[0],this}setHeader(e,t){return this._headers[e.toLowerCase()]=t,this}setHeaders(e){for(const[t,r]of Object.entries(e))this.setHeader(t,r);return this}getHeader(e){return this._headers[e.toLowerCase()]}getHeaders(){return this._headers}getHeaderNames(){return Object.keys(this._headers)}hasHeader(e){return e.toLowerCase()in this._headers}removeHeader(e){delete this._headers[e.toLowerCase()]}addTrailers(e){}flushHeaders(){}writeEarlyHints(e,t){typeof t=="function"&&t()}}const E=(()=>{const n=function(){};return n.prototype=Object.create(null),n})();function R(n={}){const e=new E,t=Array.isArray(n)||H(n)?n:Object.entries(n);for(const[r,s]of t)if(s){if(e[r]===void 0){e[r]=s;continue}e[r]=[...Array.isArray(e[r])?e[r]:[e[r]],...Array.isArray(s)?s:[s]]}return e}function H(n){return typeof n?.entries=="function"}function v(n={}){if(n instanceof Headers)return n;const e=new Headers;for(const[t,r]of Object.entries(n))if(r!==void 0){if(Array.isArray(r)){for(const s of r)e.append(t,String(s));continue}e.set(t,String(r))}return e}const S=new Set([101,204,205,304]);async function b(n,e){const t=new y,r=new w(t);t.url=e.url?.toString()||"/";let s;if(!t.url.startsWith("/")){const d=new URL(t.url);s=d.host,t.url=d.pathname+d.search+d.hash}t.method=e.method||"GET",t.headers=R(e.headers||{}),t.headers.host||(t.headers.host=e.host||s||"localhost"),t.connection.encrypted=t.connection.encrypted||e.protocol==="https",t.body=e.body||null,t.__unenv__=e.context,await n(t,r);let a=r._data;(S.has(r.statusCode)||t.method.toUpperCase()==="HEAD")&&(a=null,delete r._headers["content-length"]);const u={status:r.statusCode,statusText:r.statusMessage,headers:r._headers,body:a};return t.destroy(),r.destroy(),u}async function C(n,e,t={}){try{const r=await b(n,{url:e,...t});return new Response(r.body,{status:r.status,statusText:r.statusText,headers:v(r.headers)})}catch(r){return new Response(r.toString(),{status:Number.parseInt(r.statusCode||r.code)||500,statusText:r.statusText})}}export{y as IncomingMessage,w as ServerResponse,b as callNodeRequestHandler,C as fetchNodeRequestHandler};
